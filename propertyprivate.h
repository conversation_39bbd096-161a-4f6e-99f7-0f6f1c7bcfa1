#ifndef PROPERTYPRIVATE_H
#define PROPERTYPRIVATE_H

#include <cassert>

#include "global.h"
#include "typetraits.h"
#include "varlengtharray.h"
#include "taggedpointer.h"

class BindingStorage;

template<typename Class, typename T, auto Offset, auto Setter, auto Signal, auto Getter>
class ObjectCompatProperty;

struct BindingObserverPtr;

/**
 * @brief PropertyBindingPrivatePtr operates on a RefCountingMixin solely so that we can inline
 * the constructor and copy constructor
 */
struct RefCounted {

    int refCount() const { return ref; }
    void addRef() { ++ref; }
    bool deref() { return --ref != 0; }

private:
    int ref = 0;
};

// PropertyBindingPrivatePtr 类是一个智能指针类，用于管理 RefCounted 对象的生命周期。它包含以下成员：
// d 是一个指向 RefCounted 对象的指针。
// 构造函数和析构函数用于初始化和清理 d。
// 重载的运算符用于方便地访问 d。
// reset 方法用于重置 d。
// swap 方法用于交换两个 PropertyBindingPrivatePtr 对象的内容。
// composesEqual 函数用于比较两个 PropertyBindingPrivatePtr 对象是否相等。
class PropertyBindingPrivatePtr
{
public:
    using T = RefCounted;
    T &operator*() const { return *d; }
    T *operator->() noexcept { return d; }
    T *operator->() const noexcept { return d; }
    explicit operator T *() { return d; }
    explicit operator const T *() const noexcept { return d; }
    T *data() const noexcept { return d; }
    T *get() const noexcept { return d; }
    const T *constData() const noexcept { return d; }
    T *take() noexcept { T *x = d; d = nullptr; return x; }

    PropertyBindingPrivatePtr() noexcept : d(nullptr) { }
    ~PropertyBindingPrivatePtr()
    {
        if (d && !d->deref())
            destroyAndFreeMemory();
    }
    void destroyAndFreeMemory();

    explicit PropertyBindingPrivatePtr(T *data) noexcept : d(data) { if (d) d->addRef(); }
    PropertyBindingPrivatePtr(const PropertyBindingPrivatePtr &o) noexcept
        : d(o.d) { if (d) d->addRef(); }

    void reset(T *ptr = nullptr) noexcept;

    PropertyBindingPrivatePtr &operator=(const PropertyBindingPrivatePtr &o) noexcept
    {
        reset(o.d);
        return *this;
    }
    PropertyBindingPrivatePtr &operator=(T *o) noexcept
    {
        reset(o);
        return *this;
    }
    PropertyBindingPrivatePtr(PropertyBindingPrivatePtr &&o) noexcept : d(std::exchange(o.d, nullptr)) {}

    // MOVE_ASSIGNMENT_OPERATOR_IMPL_VIA_MOVE_AND_SWAP
    PropertyBindingPrivatePtr &operator=(PropertyBindingPrivatePtr &&other) noexcept {
            PropertyBindingPrivatePtr moved(std::move(other));
            swap(moved);
            return *this;
    }

    operator bool () const noexcept { return d != nullptr; }
    bool operator!() const noexcept { return d == nullptr; }

    void swap(PropertyBindingPrivatePtr &other) noexcept
    { std::swap(d, other.d); }

private:
    friend bool comparesEqual(const PropertyBindingPrivatePtr &lhs,
                              const PropertyBindingPrivatePtr &rhs) noexcept
    { return lhs.d == rhs.d; }
    friend bool comparesEqual(const PropertyBindingPrivatePtr &lhs,
                              const T *rhs) noexcept
    { return lhs.d == rhs; }
    friend bool comparesEqual(const PropertyBindingPrivatePtr &lhs,
                              std::nullptr_t) noexcept
    { return !lhs; }

    RefCounted *d;
};

class UntypedPropertyBinding;
class PropertyBindingPrivate;
struct PropertyBindingDataPointer;
class PropertyObserver;
struct PropertyObserverPointer;

/**
 * TODO_REMOVE_CANDIDATE: UntypedPropertyData空基类移除候选
 * - 当前是空基类，仅用于类型擦除
 * - 在移除Qt依赖后，其作用可能已经不明显
 * - 需要仔细检查是否有模板特化或类型判断依赖此基类
 * - 如果确认无依赖，可以考虑移除以进一步简化类层次
 */
class UntypedPropertyData
{
};

template <typename T>
using IsUntypedPropertyData = std::enable_if_t<std::is_base_of_v<UntypedPropertyData, T>, bool>;

template <typename T>
class PropertyData;

// Used for grouped property evaluations
class PropertyBindingData;

struct PropertyDelayedNotifications;
struct PropertyProxyBindingData
{
    // acts as QPropertyBindingData::d_ptr
    std::uintptr_t d_ptr;
    /*
        The two members below store the original binding data and property
        data pointer of the property which gets proxied.
        They are set in QPropertyDelayedNotifications::addProperty
    */
    const PropertyBindingData *originalBindingData;
    UntypedPropertyData *propertyData;
};

struct BindingEvaluationState;

// TODO_REMOVE: MSVC工作区结构体 - 仅用于MSVC C++20兼容性，可简化为void
/*  used in BindingFunctionVTable::createFor; on all other compilers, void would work, but on
    MSVC this causes C2182 when compiling in C++20 mode. As we only need to provide some default
    value which gets ignored, we introduce this dummy type.
*/
// struct MSVCWorkAround {};

// BindingFunctionVTable 是一个用于处理不同类型的可调用对象（如函数、函数对象、lambda 等）的虚表结构.
// 它通过模板和静态断言来确保类型安全，并提供了一些基本的操作函数，如调用、销毁和移动构造。
// BindingFunctionVTable 的主要作用是为不同类型的可调用对象提供一个统一的接口，使得这些对象可以在运行时被调用、销毁和移动构造。
// 它通过模板参数 Callable 和 PropertyType 来实现这一点。
struct BindingFunctionVTable
{
    // 这些类型定义了虚表中函数指针的类型：
    // CallFn 是一个函数指针类型，表示一个接受 UntypedPropertyData* 和 void* 参数并返回 bool 的函数。
    // DtorFn 是一个函数指针类型，表示一个接受 void* 参数并返回 void 的函数，用于销毁对象。
    // MoveCtrFn 是一个函数指针类型，表示一个接受两个 void* 参数并返回 void 的函数，用于移动构造对象。
    using CallFn = bool(*)(UntypedPropertyData *, void *);
    using DtorFn = void(*)(void *);
    using MoveCtrFn = void(*)(void *, void *);

    // 这些成员变量是虚表中的函数指针和对象大小：
    // call 是一个函数指针，用于调用可调用对象。
    // destroy 是一个函数指针，用于销毁可调用对象。
    // moveConstruct 是一个函数指针，用于移动构造可调用对象。
    // size 是可调用对象的大小。
    const CallFn call;
    const DtorFn destroy;
    const MoveCtrFn moveConstruct;
    const size_t size;

// createFor 模板函数用于为特定类型的 Callable 和 PropertyType 创建一个 BindingFunctionVTable 实例。
    // TODO_SIMPLIFY: 可简化为void默认参数，移除MSVCWorkAround依赖
    template<typename Callable, typename PropertyType=void> // MSVCWorkAround替换为void
    static constexpr BindingFunctionVTable createFor()
    {
        // 这个静态断言确保 Callable 的对齐要求不超过 std::max_align_t，以避免对齐问题。
        static_assert (alignof(Callable) <= alignof(std::max_align_t), "Bindings do not support overaligned functors!");
        return {
            // call 函数指针根据 Callable 和 PropertyType 的类型进行不同的处理。
            // 如果 Callable 不是可调用对象，则进行静态断言并调用未类型化的可调用对象。
            // 如果 PropertyType 不是 MSVCWorkAround，则进行类型转换并调用可调用对象，更新属性值。
            /*call=*/[](UntypedPropertyData *dataPtr, void *f){
                if constexpr (!std::is_invocable_v<Callable>) {
                    // we got an untyped callable
                    static_assert (std::is_invocable_r_v<bool, Callable, UntypedPropertyData *> );
                    auto untypedEvaluationFunction = static_cast<Callable *>(f);
                    return std::invoke(*untypedEvaluationFunction, dataPtr);
                } else if constexpr (!std::is_same_v<PropertyType, void>) { // MSVCWorkAround替换为void
                    PropertyData<PropertyType> *propertyPtr = static_cast<PropertyData<PropertyType> *>(dataPtr);
                    // That is allowed by POSIX even if Callable is a function pointer
                    auto evaluationFunction = static_cast<Callable *>(f);
                    PropertyType newValue = std::invoke(*evaluationFunction);
                    if constexpr (has_operator_equal_v<PropertyType>) {
                        if (newValue == propertyPtr->valueBypassingBindings())
                            return false;
                    }
                    propertyPtr->setValueBypassingBindings(std::move(newValue));
                    return true;
                } else {
                    // Our code will never instantiate this
                    return false;
                }
            },
            // destroy 函数指针用于销毁 Callable 对象。
            /*destroy*/[](void *f){ static_cast<Callable *>(f)->~Callable(); },
            // moveConstruct 函数指针用于移动构造 Callable 对象。
            /*moveConstruct*/[](void *addr, void *other){
                new (addr) Callable(std::move(*static_cast<Callable *>(other)));
            },
            // size 是 Callable 对象的大小。
            /*size*/sizeof(Callable)
        };
    }
};


// TODO_SIMPLIFY: 可简化默认参数，移除MSVCWorkAround依赖
template<typename Callable, typename PropertyType=void> // MSVCWorkAround替换为void
inline constexpr BindingFunctionVTable bindingFunctionVTable = BindingFunctionVTable::createFor<Callable, PropertyType>();

struct PropertyBindingFunction {
    const BindingFunctionVTable *vtable;
    void *functor;
};

using PropertyObserverCallback = void (*)(UntypedPropertyData *);
using PropertyBindingWrapper = bool(*)(UntypedPropertyData *dataPtr, PropertyBindingFunction);

class PropertyBindingData
{
    // Mutable because the address of the observer of the currently evaluating binding is stored here, for
    // notification later when the value changes.
    mutable std::uintptr_t d_ptr = 0;
    friend struct PropertyBindingDataPointer;

    friend struct PropertyDelayedNotifications;

    template<typename Class, typename T, auto Offset, auto Setter, auto Signal, auto Getter>
    friend class ObjectCompatProperty;

    DISABLE_COPY(PropertyBindingData)
public:
    PropertyBindingData() = default;
    PropertyBindingData(PropertyBindingData &&other);
    PropertyBindingData &operator=(PropertyBindingData &&other) = delete;
    ~PropertyBindingData();

    // Is d_ptr pointing to a binding (1) or list of notifiers (0)?
    static inline constexpr std::uintptr_t BindingBit = 0x1;
    // Is d_ptr pointing to QPropertyProxyBindingData (1) or to an actual binding/list of notifiers?
    static inline constexpr std::uintptr_t DelayedNotificationBit = 0x2;

    bool hasBinding() const { return d_ptr & BindingBit; }
    bool isNotificationDelayed() const { return d_ptr & DelayedNotificationBit; }

    UntypedPropertyBinding setBinding(const UntypedPropertyBinding &newBinding,
                                       UntypedPropertyData *propertyDataPtr,
                                       PropertyObserverCallback staticObserverCallback = nullptr,
                                       PropertyBindingWrapper bindingWrapper = nullptr);

    PropertyBindingPrivate *binding() const
    {
        auto dd = d();
        if (dd & BindingBit)
            return reinterpret_cast<PropertyBindingPrivate*>(dd - BindingBit);
        return nullptr;

    }

    void evaluateIfDirty(const UntypedPropertyData *) const; // ### Kept for BC reasons, unused

    void removeBinding()
    {
        if (hasBinding())
            removeBinding_helper();
    }

    void registerWithCurrentlyEvaluatingBinding(BindingEvaluationState *currentBinding) const
    {
        if (!currentBinding)
            return;
        registerWithCurrentlyEvaluatingBinding_helper(currentBinding);
    }
    void registerWithCurrentlyEvaluatingBinding() const;
    void notifyObservers(UntypedPropertyData *propertyDataPtr) const;
    void notifyObservers(UntypedPropertyData *propertyDataPtr, BindingStorage *storage) const;
private:
    std::uintptr_t &d_ref() const
    {
        std::uintptr_t &d = d_ptr;
        if (isNotificationDelayed())
            return proxyData()->d_ptr;
        return d;
    }
    std::uintptr_t d() const { return d_ref(); }
    PropertyProxyBindingData *proxyData() const
    {
        assert(isNotificationDelayed());
        return reinterpret_cast<PropertyProxyBindingData *>(d_ptr & ~(BindingBit|DelayedNotificationBit));
    }
    void registerWithCurrentlyEvaluatingBinding_helper(BindingEvaluationState *currentBinding) const;
    void removeBinding_helper();

    enum NotificationResult { Delayed, Evaluated };
    NotificationResult notifyObserver_helper(
        UntypedPropertyData *propertyDataPtr, BindingStorage *storage,
        PropertyObserverPointer observer,
        VarLengthArray<BindingObserverPtr> &bindingObservers) const;
};

// TagPreservingPointerToPointer 类用于管理带有标签的指针。它包含以下成员：
// d 是一个指向 std::uintptr_t 的指针。
// 构造函数和赋值运算符用于初始化和设置 d。
// clear 方法用于清空 d。
// setPointer 方法用于设置指针，并保留标签。
// get 方法用于获取指针。
// 重载的布尔运算符用于检查 d 是否为空。
template <typename T, typename Tag>
class TagPreservingPointerToPointer
{
public:
    constexpr TagPreservingPointerToPointer() = default;

    TagPreservingPointerToPointer(T **ptr)
        : d(reinterpret_cast<std::uintptr_t*>(ptr))
    {}

    TagPreservingPointerToPointer<T, Tag> &operator=(T **ptr)
    {
        d = reinterpret_cast<std::uintptr_t *>(ptr);
        return *this;
    }

    TagPreservingPointerToPointer<T, Tag> &operator=(TaggedPointer<T, Tag> *ptr)
    {
        d = reinterpret_cast<std::uintptr_t *>(ptr);
        return *this;
    }

    void clear()
    {
        d = nullptr;
    }

    void setPointer(T *ptr)
    {
        *d = reinterpret_cast<std::uintptr_t>(ptr) | (*d & TaggedPointer<T, Tag>::tagMask());
    }

    T *get() const
    {
        return reinterpret_cast<T*>(*d & TaggedPointer<T, Tag>::pointerMask());
    }

    explicit operator bool() const
    {
        return d != nullptr;
    }

private:
    std::uintptr_t *d = nullptr;
};

// TODO_REMOVE: detail命名空间工具 - 仅用于ObjectCompatProperty，核心API未使用
/*
namespace detail {
template <typename F>
struct ExtractClassFromFunctionPointer;

template<typename T, typename C>
struct ExtractClassFromFunctionPointer<T C::*> { using Class = C; };

constexpr size_t getOffset(size_t o)
{
    return o;
}
constexpr size_t getOffset(size_t (*offsetFn)())
{
    return offsetFn();
}
}
*/

#endif // PROPERTYPRIVATE_H
