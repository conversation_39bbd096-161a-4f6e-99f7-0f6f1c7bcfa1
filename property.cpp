
#include "property.h"
#include "property_p.h"


void PropertyBindingPrivatePtr::destroyAndFreeMemory()
{
    PropertyBindingPrivate::destroyAndFreeMemory(static_cast<PropertyBindingPrivate *>(d));
}

void PropertyBindingPrivatePtr::reset(RefCounted *ptr) noexcept
{
    if (ptr != d) {
        if (ptr)
            ptr->addRef();
        auto *old = std::exchange(d, ptr);
        if (old && !old->deref())
            PropertyBindingPrivate::destroyAndFreeMemory(static_cast<PropertyBindingPrivate *>(d));
    }
}

void PropertyBindingDataPointer::addObserver(PropertyObserver *observer)
{
    if (auto *b = binding()) {
        observer->prev = &b->firstObserver.ptr;
        observer->next = b->firstObserver.ptr;
        if (observer->next)
            observer->next->prev = &observer->next;
        b->firstObserver.ptr = observer;
    } else {
        auto &d = ptr->d_ref();
        assert(!(d & PropertyBindingData::BindingBit));
        auto firstObserver = reinterpret_cast<PropertyObserver*>(d);
        observer->prev = reinterpret_cast<PropertyObserver**>(&d);
        observer->next = firstObserver;
        if (observer->next)
            observer->next->prev = &observer->next;
        d = reinterpret_cast<std::uintptr_t>(observer);
    }
}

struct PropertyDelayedNotifications
{
    // we can't access the dynamic page size as we need a constant value
    // use 4096 as a sensible default
    static constexpr inline auto PageSize = 4096;
    int ref = 0;
    PropertyDelayedNotifications *next = nullptr; // in case we have more than size dirty properties...
    sizetype used = 0;
    // Size chosen to avoid allocating more than one page of memory, while still ensuring
    // that we can store many delayed properties without doing further allocations
    static constexpr sizetype size = (PageSize - 3*sizeof(void *))/sizeof(PropertyProxyBindingData);
    PropertyProxyBindingData delayedProperties[size];

    void addProperty(const PropertyBindingData *bindingData, UntypedPropertyData *propertyData) {
        if (bindingData->isNotificationDelayed())
            return;
        auto *data = this;
        while (data->used == size) {
            if (!data->next)
                // add a new page
                data->next = new PropertyDelayedNotifications;
            data = data->next;
        }
        auto *delayed = data->delayedProperties + data->used;
        *delayed = PropertyProxyBindingData { bindingData->d_ptr, bindingData, propertyData };
        ++data->used;
        // preserve the binding bit for faster access
        std::uintptr_t bindingBit = bindingData->d_ptr & PropertyBindingData::BindingBit;
        bindingData->d_ptr = reinterpret_cast<std::uintptr_t>(delayed) | PropertyBindingData::DelayedNotificationBit | bindingBit;
        assert(bindingData->d_ptr > 3);
        if (!bindingBit) {
            if (auto observer = reinterpret_cast<PropertyObserver *>(delayed->d_ptr))
                observer->prev = reinterpret_cast<PropertyObserver **>(&delayed->d_ptr);
        }
    }

    void evaluateBindings(PendingBindingObserverList &bindingObservers, sizetype index, BindingStatus *status) {
        auto *delayed = delayedProperties + index;
        auto *bindingData = delayed->originalBindingData;
        if (!bindingData)
            return;

        bindingData->d_ptr = delayed->d_ptr;
        assert(!(bindingData->d_ptr & PropertyBindingData::DelayedNotificationBit));
        if (!bindingData->hasBinding()) {
            if (auto observer = reinterpret_cast<PropertyObserver *>(bindingData->d_ptr))
                observer->prev = reinterpret_cast<PropertyObserver **>(&bindingData->d_ptr);
        }

        PropertyBindingDataPointer bindingDataPointer{bindingData};
        PropertyObserverPointer observer = bindingDataPointer.firstObserver();
        if (observer)
            observer.evaluateBindings(bindingObservers, status);
    }


    void notify(sizetype index) {
        auto *delayed = delayedProperties + index;
        if (delayed->d_ptr  & PropertyBindingData::BindingBit)
            return; // already handled
        if (!delayed->originalBindingData)
            return;
        delayed->originalBindingData = nullptr;

        PropertyObserverPointer observer  { reinterpret_cast<PropertyObserver *>(delayed->d_ptr & ~PropertyBindingData::DelayedNotificationBit) };
        delayed->d_ptr = 0;

        if (observer)
            observer.notify(delayed->propertyData);
    }
};

static thread_local BindingStatus bindingStatus;

void beginPropertyUpdateGroup()
{
    PropertyDelayedNotifications *& groupUpdateData = bindingStatus.groupUpdateData;
    if (!groupUpdateData)
        groupUpdateData = new PropertyDelayedNotifications;
    ++groupUpdateData->ref;
}

void endPropertyUpdateGroup()
{
    auto status = &bindingStatus;
    PropertyDelayedNotifications *& groupUpdateData = status->groupUpdateData;
    auto *data = groupUpdateData;
    assert(data->ref);
    if (--data->ref)
        return;
    groupUpdateData = nullptr;
    // ensures that bindings are kept alive until endPropertyUpdateGroup concludes
    PendingBindingObserverList bindingObservers;
    // update all delayed properties
    auto start = data;
    while (data) {
        for (sizetype i = 0; i < data->used; ++i)
            data->evaluateBindings(bindingObservers, i, status);
        data = data->next;
    }
    // notify all delayed notifications from binding evaluation
    for (const BindingObserverPtr &observer: bindingObservers) {
        PropertyBindingPrivate *binding = observer.binding();
        binding->notifyNonRecursive();
    }
    // do the same for properties which only have observers
    data = start;
    while (data) {
        for (sizetype i = 0; i < data->used; ++i)
            data->notify(i);
        delete std::exchange(data, data->next);
    }
}

static_assert(std::is_trivially_destructible_v<PropertyBindingSourceLocation>);
static_assert(std::is_trivially_destructible_v<std::byte[sizeof(PropertyBindingSourceLocation)]>);

PropertyBindingPrivate::~PropertyBindingPrivate()
{
    if (firstObserver)
        firstObserver.unlink();
    if (vtable->size)
        vtable->destroy(reinterpret_cast<std::byte *>(this)
                        + PropertyBindingPrivate::getSizeEnsuringAlignment());
}


void PropertyBindingPrivate::clearDependencyObservers() {
    size_t min = dependencyObserverCount < inlineDependencyObservers.size() ? dependencyObserverCount : inlineDependencyObservers.size();
    for (size_t i = 0; i < min; ++i) {
        PropertyObserverPointer p{&inlineDependencyObservers[i]};
        p.unlink_fast();
    }
    if (heapObservers)
        heapObservers->clear();
    dependencyObserverCount = 0;
}

PropertyObserverPointer PropertyBindingPrivate::allocateDependencyObserver_slow()
{
    ++dependencyObserverCount;
    if (!heapObservers)
        heapObservers.reset(new std::vector<PropertyObserver>());
    return {&heapObservers->emplace_back()};
}

void PropertyBindingPrivate::unlinkAndDeref()
{
    clearDependencyObservers();
    propertyDataPtr = nullptr;
    if (!deref())
        destroyAndFreeMemory(this);
}

bool PropertyBindingPrivate::evaluateRecursive(PendingBindingObserverList &bindingObservers, BindingStatus *status)
{
    if (!status)
        status = &bindingStatus;
    return evaluateRecursive_inline(bindingObservers, status);
}

void PropertyBindingPrivate::notifyNonRecursive(const PendingBindingObserverList &bindingObservers)
{
    notifyNonRecursive();
    for (auto &&bindingObserver: bindingObservers) {
        bindingObserver.binding()->notifyNonRecursive();
    }
}

PropertyBindingPrivate::NotificationState PropertyBindingPrivate::notifyNonRecursive()
{
    if (!pendingNotify)
        return Delayed;
    pendingNotify = false;
    assert(!updating);
    updating = true;
    if (firstObserver) {
        firstObserver.noSelfDependencies(this);
        firstObserver.notify(propertyDataPtr);
    }
    if (hasStaticObserver)
        staticObserverCallback(propertyDataPtr);
    updating = false;
    return Sent;
}

UntypedPropertyBinding::UntypedPropertyBinding() = default;

UntypedPropertyBinding::UntypedPropertyBinding(const BindingFunctionVTable *vtable, void *function,
                                                 const PropertyBindingSourceLocation &location)
{
    std::byte *mem = new std::byte[PropertyBindingPrivate::getSizeEnsuringAlignment() + vtable->size]();
    d = new(mem) PropertyBindingPrivate(vtable, std::move(location));
    vtable->moveConstruct(mem + PropertyBindingPrivate::getSizeEnsuringAlignment(), function);
}

UntypedPropertyBinding::UntypedPropertyBinding(UntypedPropertyBinding &&other)
    : d(std::move(other.d))
{
}

UntypedPropertyBinding::UntypedPropertyBinding(const UntypedPropertyBinding &other)
    : d(other.d)
{
}

UntypedPropertyBinding &UntypedPropertyBinding::operator=(const UntypedPropertyBinding &other)
{
    d = other.d;
    return *this;
}

UntypedPropertyBinding &UntypedPropertyBinding::operator=(UntypedPropertyBinding &&other)
{
    d = std::move(other.d);
    return *this;
}

UntypedPropertyBinding::UntypedPropertyBinding(PropertyBindingPrivate *priv)
    : d(priv)
{
}

UntypedPropertyBinding::~UntypedPropertyBinding()
{
}

bool UntypedPropertyBinding::isNull() const
{
    return !d;
}

PropertyBindingError UntypedPropertyBinding::error() const
{
    if (!d)
        return PropertyBindingError();
    return static_cast<PropertyBindingPrivate *>(d.get())->bindingError();
}




PropertyBindingData::~PropertyBindingData()
{
    PropertyBindingDataPointer d{this};
    if (isNotificationDelayed())
        proxyData()->originalBindingData = nullptr;
    for (auto observer = d.firstObserver(); observer;) {
        auto next = observer.nextObserver();
        observer.unlink();
        observer = next;
    }
    if (auto binding = d.binding())
        binding->unlinkAndDeref();
}


UntypedPropertyBinding PropertyBindingData::setBinding(const UntypedPropertyBinding &binding,
                                                         UntypedPropertyData *propertyDataPtr,
                                                         PropertyObserverCallback staticObserverCallback,
                                                         PropertyBindingWrapper guardCallback)
{
    PropertyBindingPrivatePtr oldBinding;
    PropertyBindingPrivatePtr newBinding = binding.d;

    PropertyBindingDataPointer d{this};
    PropertyObserverPointer observer;

    auto &data = d_ref();
    if (auto *existingBinding = d.binding()) {
        if (existingBinding == newBinding.data())
            return UntypedPropertyBinding(static_cast<PropertyBindingPrivate *>(oldBinding.data()));
        if (existingBinding->isUpdating()) {
            existingBinding->setError({PropertyBindingError::BindingLoop, "Binding set during binding evaluation!"});
            return UntypedPropertyBinding(static_cast<PropertyBindingPrivate *>(oldBinding.data()));
        }
        oldBinding = PropertyBindingPrivatePtr(existingBinding);
        observer = static_cast<PropertyBindingPrivate *>(oldBinding.data())->takeObservers();
        static_cast<PropertyBindingPrivate *>(oldBinding.data())->unlinkAndDeref();
        data = 0;
    } else {
        observer = d.firstObserver();
    }

    if (newBinding) {
        newBinding.data()->addRef();
        data = reinterpret_cast<std::uintptr_t>(newBinding.data());
        data |= BindingBit;
        auto newBindingRaw = static_cast<PropertyBindingPrivate *>(newBinding.data());
        newBindingRaw->setProperty(propertyDataPtr);
        if (observer)
            newBindingRaw->prependObserver(observer);
        newBindingRaw->setStaticObserver(staticObserverCallback, guardCallback);

        PendingBindingObserverList bindingObservers;
        newBindingRaw->evaluateRecursive(bindingObservers);
        newBindingRaw->notifyNonRecursive(bindingObservers);
    } else if (observer) {
        d.setObservers(observer.ptr);
    } else {
        data = 0;
    }

    if (oldBinding)
        static_cast<PropertyBindingPrivate *>(oldBinding.data())->detachFromProperty();

    return UntypedPropertyBinding(static_cast<PropertyBindingPrivate *>(oldBinding.data()));
}

PropertyBindingData::PropertyBindingData(PropertyBindingData &&other) : d_ptr(std::exchange(other.d_ptr, 0))
{
    PropertyBindingDataPointer::fixupAfterMove(this);
}

BindingEvaluationState::BindingEvaluationState(PropertyBindingPrivate *binding, BindingStatus *status)
    : binding(binding)
{
    assert(status);
    BindingStatus *s = status;
    // store a pointer to the currentBindingEvaluationState to avoid a TLS lookup in
    // the destructor (as these come with a non zero cost)
    currentState = &s->currentlyEvaluatingBinding;
    previousState = *currentState;
    *currentState = this;
    binding->clearDependencyObservers();
}

CompatPropertySafePoint::CompatPropertySafePoint(BindingStatus *status, UntypedPropertyData *property)
    : property(property)
{
    // store a pointer to the currentBindingEvaluationState to avoid a TLS lookup in
    // the destructor (as these come with a non zero cost)
    currentState = &status->currentCompatProperty;
    previousState = *currentState;
    *currentState = this;

    currentlyEvaluatingBindingList = &bindingStatus.currentlyEvaluatingBinding;
    bindingState = *currentlyEvaluatingBindingList;
    *currentlyEvaluatingBindingList = nullptr;
}

PropertyBindingPrivate *PropertyBindingPrivate::currentlyEvaluatingBinding()
{
    auto currentState = bindingStatus.currentlyEvaluatingBinding ;
    return currentState ? currentState->binding : nullptr;
}



void PropertyBindingData::removeBinding_helper()
{
    PropertyBindingDataPointer d{this};

    auto *existingBinding = d.binding();
    assert(existingBinding);
    if (existingBinding->isSticky()) {
        return;
    }

    auto observer = existingBinding->takeObservers();
    d_ref() = 0;
    if (observer)
        d.setObservers(observer.ptr);
    existingBinding->unlinkAndDeref();
}

void PropertyBindingData::registerWithCurrentlyEvaluatingBinding() const
{
    auto currentState = bindingStatus.currentlyEvaluatingBinding;
    if (!currentState)
        return;
    registerWithCurrentlyEvaluatingBinding_helper(currentState);
}


void PropertyBindingData::registerWithCurrentlyEvaluatingBinding_helper(BindingEvaluationState *currentState) const
{
    PropertyBindingDataPointer d{this};
    const auto v = currentState->alreadyCaptureProperties;
    const auto it = std::find(v.begin(), v.end(), this);
    if (it != v.end())
        return;
    else
        currentState->alreadyCaptureProperties.push_back(this);

    PropertyObserverPointer dependencyObserver = currentState->binding->allocateDependencyObserver();
    assert(PropertyObserver::ObserverNotifiesBinding == 0);
    dependencyObserver.setBindingToNotify_unsafe(currentState->binding);
    d.addObserver(dependencyObserver.ptr);
}

void PropertyBindingData::notifyObservers(UntypedPropertyData *propertyDataPtr) const
{
    notifyObservers(propertyDataPtr, nullptr);
}

void PropertyBindingData::notifyObservers(UntypedPropertyData *propertyDataPtr, void *storage) const
{
    if (isNotificationDelayed())
        return;
    PropertyBindingDataPointer d{this};

    PendingBindingObserverList bindingObservers;
    if (PropertyObserverPointer observer = d.firstObserver()) {
        if (notifyObserver_helper(propertyDataPtr, storage, observer, bindingObservers) == Evaluated) {
            /* evaluateBindings() can trash the observers. We need to re-fetch here.
             "this" might also no longer be valid in case we have a QObjectBindableProperty
             and consequently d isn't either (this happens when binding evaluation has
             caused the binding storage to resize.
             If storage is nullptr, then there is no dynamically resizable storage,
             and we cannot run into the issue.
            */
            if (storage)
                d = PropertyBindingDataPointer {storage->bindingData(propertyDataPtr)};
            if (PropertyObserverPointer observer = d.firstObserver())
                observer.notify(propertyDataPtr);
            for (auto &&bindingObserver: bindingObservers)
                bindingObserver.binding()->notifyNonRecursive();
        }
    }
}

PropertyBindingData::NotificationResult PropertyBindingData::notifyObserver_helper
    (
        UntypedPropertyData *propertyDataPtr, void *storage,
        PropertyObserverPointer observer,
        PendingBindingObserverList &bindingObservers) const
{
    // TODO_SIMPLIFY: 可简化线程ID检查，移除条件编译
    /*
#ifdef QT_HAS_FAST_CURRENT_THREAD_ID
    BindingStatus *status = storage ? storage->bindingStatus : nullptr;
    if (!status || status->threadId != QThread::currentThreadId())
        status = &bindingStatus;
#else
    */
    (void)storage;
    BindingStatus *status = &bindingStatus;
    /*
#endif
    */
    if (PropertyDelayedNotifications *delay = status->groupUpdateData) {
        delay->addProperty(this, propertyDataPtr);
        return Delayed;
    }

    observer.evaluateBindings(bindingObservers, status);
    return Evaluated;
}


PropertyObserver::PropertyObserver(ChangeHandler changeHandler)
{
    PropertyObserverPointer d{this};
    d.setChangeHandler(changeHandler);
}

void PropertyObserver::setSource(const PropertyBindingData &property)
{
    PropertyObserverPointer d{this};
    PropertyBindingDataPointer propPrivate{&property};
    d.observeProperty(propPrivate);
}

PropertyObserver::~PropertyObserver()
{
    PropertyObserverPointer d{this};
    d.unlink();
}

PropertyObserver::PropertyObserver(PropertyObserver &&other) noexcept
{
    binding = std::exchange(other.binding, {});
    next = std::exchange(other.next, {});
    prev = std::exchange(other.prev, {});
    if (next)
        next->prev = &next;
    if (prev)
        prev.setPointer(this);
}

PropertyObserver &PropertyObserver::operator=(PropertyObserver &&other) noexcept
{
    if (this == &other)
        return *this;

    PropertyObserverPointer d{this};
    d.unlink();
    binding = nullptr;

    binding = std::exchange(other.binding, {});
    next = std::exchange(other.next, {});
    prev = std::exchange(other.prev, {});
    if (next)
        next->prev = &next;
    if (prev)
        prev.setPointer(this);

    return *this;
}

void PropertyObserverPointer::setChangeHandler(PropertyObserver::ChangeHandler changeHandler)
{
    assert(ptr->next.tag() != PropertyObserver::ObserverIsPlaceholder);
    ptr->changeHandler = changeHandler;
    ptr->next.setTag(PropertyObserver::ObserverNotifiesChangeHandler);
}

void PropertyObserverPointer::setBindingToNotify_unsafe(PropertyBindingPrivate *binding)
{
    assert(ptr->next.tag() == PropertyObserver::ObserverNotifiesBinding);
    ptr->binding = binding;
}

// TODO_SIMPLIFY: 调试模式下的自依赖检查 - 简化的调试辅助代码
#ifndef QT_NO_DEBUG
void PropertyObserverPointer::noSelfDependencies(PropertyBindingPrivate *binding)
{
    auto observer = const_cast<PropertyObserver*>(ptr);
    // See also comment in notify()
    while (observer) {
        if (PropertyObserver::ObserverTag(observer->next.tag()) == PropertyObserver::ObserverNotifiesBinding)
            if (observer->binding == binding) {
                std::cerr << "Property depends on itself!";
                break;
            }

        observer = observer->next.data();
    }
}
#endif

void PropertyObserverPointer::evaluateBindings(PendingBindingObserverList &bindingObservers, BindingStatus *status)
{
    assert(status);
    auto observer = const_cast<PropertyObserver*>(ptr);
    // See also comment in notify()
    while (observer) {
        PropertyObserver *next = observer->next.data();

        if (PropertyObserver::ObserverTag(observer->next.tag()) == PropertyObserver::ObserverNotifiesBinding) {
            auto bindingToEvaluate = observer->binding;
            PropertyObserverNodeProtector protector(observer);
            BindingObserverPtr bindingObserver(observer); // binding must not be gone after evaluateRecursive_inline
            if (bindingToEvaluate->evaluateRecursive_inline(bindingObservers, status))
                bindingObservers.push_back(std::move(bindingObserver));
            next = protector.next();
        }

        observer = next;
    }
}

void PropertyObserverPointer::observeProperty(PropertyBindingDataPointer property)
{
    if (ptr->prev)
        unlink();
    property.addObserver(ptr);
}

PropertyBindingError::PropertyBindingError(Type type, std::string_view description)
    : m_type(type), m_description(description)
{
}


// TODO_REMOVE: BindingStorage相关实现已移除
// 原因：ObjectCompatProperty已被注释移除，BindingStorage没有活跃的使用者
// 当前Property<T>类直接使用PropertyBindingData，不依赖BindingStorage
// 移除收益：简化项目结构，减少维护复杂性，提高代码清晰度

// TODO_REMOVE: BindingStorage类实现已移除
// 原因：ObjectCompatProperty已被注释移除，BindingStorage没有活跃的使用者
// 当前Property<T>类直接使用PropertyBindingData，不依赖BindingStorage
