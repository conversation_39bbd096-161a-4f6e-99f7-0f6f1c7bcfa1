#ifndef BINDINGSTORAGE_H
#define BINDINGSTORAGE_H

typedef void * HANDLE;  // #include <QtCore/qnamespace.h>

template <typename Class, typename T, auto Offset, auto Setter, auto Signal, auto Getter>
class ObjectCompatProperty;
struct PropertyDelayedNotifications;
class UntypedPropertyData;

class PropertyBindingData;
struct BindingEvaluationState;
struct CompatPropertySafePoint;

struct BindingStatus
{
    BindingEvaluationState *currentlyEvaluatingBinding = nullptr;
    CompatPropertySafePoint *currentCompatProperty = nullptr;
    HANDLE threadId = nullptr;
    PropertyDelayedNotifications *groupUpdateData = nullptr;
};

// TODO_REMOVE: BindingStatusAccessToken相关 - 核心API调用链中未使用访问令牌机制
/*
struct BindingStatusAccessToken;
BindingStatus *getBindingStatus(BindingStatusAccessToken);
*/

struct BindingStorageData;

/**
 * TODO_SIMPLIFY: BindingStorage架构简化方案
 *
 * 当前架构问题：
 * - 包含已移除的Qt特有功能残留（线程移动、访问令牌等）
 * - 复杂的友元关系网络（ObjectCompatProperty、PropertyBindingData）
 * - 双重间接层：BindingStorage -> BindingStorageData -> PropertyBindingData
 * - 职责重叠：与PropertyBindingData功能部分重复
 *
 * 简化收益：
 * - 减少内存间接访问，提高性能
 * - 简化调试和维护复杂度
 * - 更清晰的职责分离
 *
 * 简化方案：
 * 1. 直接合并方案：将BindingStorage功能合并到PropertyBindingData
 * 2. 接口简化方案：保留BindingStorage但移除BindingStorageData中间层
 * 3. 职责重新分配：BindingStorage专注于全局状态，PropertyBindingData专注于单个属性
 *
 * 技术风险：
 * - ObjectCompatProperty的重构成本
 * - 全局绑定状态管理的复杂性
 * - 现有API兼容性
 *
 * 推荐方案：先实施方案2（移除中间层），再评估方案1的可行性
 */
class BindingStorage
{
    mutable BindingStorageData *d = nullptr;
    BindingStatus *bindingStatus = nullptr;

    template<typename Class, typename T, auto Offset, auto Setter, auto Signal, auto Getter>
    friend class ObjectCompatProperty;
    // TODO_REMOVE: ObjectPrivate友元声明 - 核心API未使用ObjectPrivate类
    // friend class ObjectPrivate;
    friend class PropertyBindingData;
public:
    BindingStorage();
    ~BindingStorage();

    bool isEmpty() { return !d; }
    bool isValid() const noexcept { return bindingStatus; }

    // TODO_REMOVE: status访问方法 - 核心API调用链中未使用访问令牌机制
    // const BindingStatus *status(BindingStatusAccessToken) const;

    void registerDependency(const UntypedPropertyData *data) const
    {
        if (!bindingStatus || !bindingStatus->currentlyEvaluatingBinding)
            return;
        registerDependency_helper(data);
    }
    PropertyBindingData *bindingData(const UntypedPropertyData *data) const
    {
        if (!d)
            return nullptr;
        return bindingData_helper(data);
    }

    PropertyBindingData *bindingData(UntypedPropertyData *data, bool create)
    {
        if (!d && !create)
            return nullptr;
        return bindingData_helper(data, create);
    }
private:
    // TODO_REMOVE: 线程移动和清理方法 - 核心API调用链中未使用线程移动功能
    // void reinitAfterThreadMove();
    // void clear();
    void registerDependency_helper(const UntypedPropertyData *data) const;

    PropertyBindingData *bindingData_helper(const UntypedPropertyData *data) const;
    PropertyBindingData *bindingData_helper(UntypedPropertyData *data, bool create);
};

#endif // BINDINGSTORAGE_H
