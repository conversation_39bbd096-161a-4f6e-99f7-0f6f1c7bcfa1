cmake_minimum_required(VERSION 3.16)
project(property)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(MODULE property)
set(MODULE_SRC
    property.h
    propertyprivate.h
    property_p.h
    property.cpp
    utils/shareddata.h
    utils/taggedpointer.h
    utils/varlengtharray.h
    bindingstorage.h
    main.cpp
)

add_executable(${MODULE} ${MODULE_SRC})

target_include_directories(${MODULE} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/global
    ${CMAKE_CURRENT_SOURCE_DIR}/global/types
    ${CMAKE_CURRENT_SOURCE_DIR}/utils
)
