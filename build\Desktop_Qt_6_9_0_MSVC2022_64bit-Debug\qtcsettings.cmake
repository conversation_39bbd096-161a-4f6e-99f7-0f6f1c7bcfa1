# This file is managed by Qt Creator, do not edit!

set("CMAKE_COLOR_DIAGNOSTICS" "ON" CACHE "BOOL" "" FORCE)
set("CMAKE_PREFIX_PATH" "C:/Qt/6.9.0/msvc2022_64" CACHE "PATH" "" FORCE)
set("QT_QMAKE_EXECUTABLE" "C:/Qt/6.9.0/msvc2022_64/bin/qmake.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PROJECT_INCLUDE_BEFORE" "E:/Projects/Code/AI Agent/property/build/Desktop_Qt_6_9_0_MSVC2022_64bit-Debug/.qtc/package-manager/auto-setup.cmake" CACHE "FILEPATH" "" FORCE)
set("CMAKE_CXX_FLAGS_INIT" "-DQT_QML_DEBUG" CACHE "STRING" "" FORCE)
set("CMAKE_GENERATOR" "Ninja" CACHE "STRING" "" FORCE)
set("QT_QML_GENERATE_QMLLS_INI" "ON" CACHE "BOOL" "" FORCE)
set("QT_MAINTENANCE_TOOL" "C:/Qt/MaintenanceTool.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_CXX_COMPILER" "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/cl.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_BUILD_TYPE" "Debug" CACHE "STRING" "" FORCE)
set("CMAKE_C_COMPILER" "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/cl.exe" CACHE "FILEPATH" "" FORCE)