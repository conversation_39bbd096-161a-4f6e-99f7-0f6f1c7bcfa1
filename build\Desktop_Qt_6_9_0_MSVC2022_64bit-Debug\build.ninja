# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: property
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = E$:\Projects\Code\AI$ Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug\
# =============================================================================
# Object build statements for EXECUTABLE target property


#############################################
# Order-only phony target for property

build cmake_object_order_depends_target_property: phony || .

build CMakeFiles\property.dir\property.cpp.obj: CXX_COMPILER__property_unscanned_Debug E$:\Projects\Code\AI$ Agent\property\property.cpp || cmake_object_order_depends_target_property
  FLAGS = -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd
  INCLUDES = -I"E:\Projects\Code\AI Agent\property" -I"E:\Projects\Code\AI Agent\property\global" -I"E:\Projects\Code\AI Agent\property\global\types" -I"E:\Projects\Code\AI Agent\property\utils"
  OBJECT_DIR = CMakeFiles\property.dir
  OBJECT_FILE_DIR = CMakeFiles\property.dir
  TARGET_COMPILE_PDB = CMakeFiles\property.dir\
  TARGET_PDB = property.pdb

build CMakeFiles\property.dir\main.cpp.obj: CXX_COMPILER__property_unscanned_Debug E$:\Projects\Code\AI$ Agent\property\main.cpp || cmake_object_order_depends_target_property
  FLAGS = -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd
  INCLUDES = -I"E:\Projects\Code\AI Agent\property" -I"E:\Projects\Code\AI Agent\property\global" -I"E:\Projects\Code\AI Agent\property\global\types" -I"E:\Projects\Code\AI Agent\property\utils"
  OBJECT_DIR = CMakeFiles\property.dir
  OBJECT_FILE_DIR = CMakeFiles\property.dir
  TARGET_COMPILE_PDB = CMakeFiles\property.dir\
  TARGET_PDB = property.pdb


# =============================================================================
# Link build statements for EXECUTABLE target property


#############################################
# Link the executable property.exe

build property.exe: CXX_EXECUTABLE_LINKER__property_Debug CMakeFiles\property.dir\property.cpp.obj CMakeFiles\property.dir\main.cpp.obj
  FLAGS = -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\property.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\property.dir\
  TARGET_FILE = property.exe
  TARGET_IMPLIB = property.lib
  TARGET_PDB = property.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D "E:\Projects\Code\AI Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug" && C:\Qt\Tools\CMake_64\bin\cmake-gui.exe -S"E:\Projects\Code\AI Agent\property" -B"E:\Projects\Code\AI Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug""
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D "E:\Projects\Code\AI Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug" && C:\Qt\Tools\CMake_64\bin\cmake.exe --regenerate-during-build -S"E:\Projects\Code\AI Agent\property" -B"E:\Projects\Code\AI Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util

# =============================================================================
# Target aliases.

build property: phony property.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: E:/Projects/Code/AI Agent/property/build/Desktop_Qt_6_9_0_MSVC2022_64bit-Debug

build all: phony property.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc\package-manager\auto-setup.cmake .qtc\package-manager\maintenance_tool_provider.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCCompilerABI.c C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeNinjaFindMake.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystem.cmake.in C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.30.5\CMakeCCompiler.cmake CMakeFiles\3.30.5\CMakeCXXCompiler.cmake CMakeFiles\3.30.5\CMakeRCCompiler.cmake CMakeFiles\3.30.5\CMakeSystem.cmake E$:\Projects\Code\AI$ Agent\property\CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc\package-manager\auto-setup.cmake .qtc\package-manager\maintenance_tool_provider.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCCompilerABI.c C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeNinjaFindMake.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystem.cmake.in C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.30.5\CMakeCCompiler.cmake CMakeFiles\3.30.5\CMakeCXXCompiler.cmake CMakeFiles\3.30.5\CMakeRCCompiler.cmake CMakeFiles\3.30.5\CMakeSystem.cmake E$:\Projects\Code\AI$ Agent\property\CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
